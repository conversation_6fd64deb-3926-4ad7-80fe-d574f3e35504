-- =====================================================
-- MaSuiteHomes to HuskHomes Migration Script
-- =====================================================
-- This script migrates player home data from MaSuiteHomes to HuskHomes
-- 
-- IMPORTANT: 
-- 1. Backup your databases before running this script!
-- 2. Test on a copy of your database first
-- 3. Adjust table names if your HuskHomes uses different table names
-- 4. Update server names in the mapping section below
-- =====================================================

-- Step 1: Create temporary mapping table for server names
-- MaSuiteHomes uses server names, HuskHomes uses server IDs
-- Update this mapping to match your server configuration
CREATE TEMPORARY TABLE server_mapping (
    masuite_server VARCHAR(100),
    huskhomes_server VARCHAR(100)
);

-- Insert your server mappings here - CUSTOMIZE THIS SECTION
INSERT INTO server_mapping (masuite_server, huskhomes_server) VALUES
('survival', 'survival'),
('creative', 'creative');
-- Add more server mappings as needed

-- Step 2: Insert player data into HuskHomes users table
-- This ensures all home owners exist in the HuskHomes user system
INSERT IGNORE INTO huskhomes_users (uuid, username, last_position, home_slots, offline)
SELECT DISTINCT 
    m.owner as uuid,
    'Unknown' as username,  -- HuskHomes will update this when player joins
    NULL as last_position,
    10 as home_slots,       -- Default home slots, adjust as needed
    0 as offline
FROM masuite_homes m;

-- Step 3: Insert position data into HuskHomes position table
-- Each home needs a position record
INSERT INTO huskhomes_position_data (id, x, y, z, yaw, pitch, world_name, world_uuid, server_name)
SELECT 
    m.id + 100000 as id,  -- Offset to avoid ID conflicts
    m.x,
    m.y, 
    m.z,
    COALESCE(m.yaw, 0.0) as yaw,
    COALESCE(m.pitch, 0.0) as pitch,
    m.world as world_name,
    '00000000-0000-0000-0000-000000000000' as world_uuid,  -- Default UUID, HuskHomes will update
    COALESCE(sm.huskhomes_server, m.server) as server_name
FROM masuite_homes m
LEFT JOIN server_mapping sm ON m.server = sm.masuite_server;

-- Step 4: Insert home data into HuskHomes homes table
INSERT INTO huskhomes_homes (id, uuid, name, position_id, description, creation_time, public)
SELECT 
    m.id,
    m.owner as uuid,
    m.name,
    m.id + 100000 as position_id,  -- Reference to position data
    CONCAT('Migrated from MaSuiteHomes on server: ', m.server) as description,
    NOW() as creation_time,
    0 as public  -- Set all homes as private by default
FROM masuite_homes m;

-- Step 5: Clean up temporary table
DROP TEMPORARY TABLE server_mapping;

-- =====================================================
-- Migration Summary Query
-- Run this to verify the migration results
-- =====================================================
SELECT 
    'Migration Summary' as info,
    (SELECT COUNT(*) FROM masuite_homes) as masuite_homes_count,
    (SELECT COUNT(*) FROM huskhomes_homes) as huskhomes_homes_count,
    (SELECT COUNT(*) FROM huskhomes_position_data WHERE id > 100000) as migrated_positions_count,
    (SELECT COUNT(DISTINCT uuid) FROM huskhomes_users) as total_users_count;

-- =====================================================
-- Verification Queries
-- =====================================================

-- Check for any homes that failed to migrate
SELECT 'Homes missing position data' as issue, COUNT(*) as count
FROM huskhomes_homes h
LEFT JOIN huskhomes_position_data p ON h.position_id = p.id
WHERE p.id IS NULL;

-- Check for duplicate home names per user (HuskHomes doesn't allow this)
SELECT 'Users with duplicate home names' as issue, uuid, name, COUNT(*) as count
FROM huskhomes_homes 
GROUP BY uuid, name 
HAVING COUNT(*) > 1;

-- Show sample of migrated data
SELECT 'Sample migrated homes' as info, h.name, h.uuid, p.world_name, p.server_name, p.x, p.y, p.z
FROM huskhomes_homes h
JOIN huskhomes_position_data p ON h.position_id = p.id
LIMIT 10;
