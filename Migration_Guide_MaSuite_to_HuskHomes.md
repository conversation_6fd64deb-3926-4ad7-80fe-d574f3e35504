# MaSuiteHomes to HuskHomes Migration Guide

## Overview
This guide helps you migrate player home data from MaSuiteHomes to HuskHomes. The migration preserves all home locations, names, and ownership while adapting to HuskHomes' database structure.

## Prerequisites
- **Backup your databases** before starting migration
- HuskHomes plugin installed and configured
- Access to both MaSuiteHomes and HuskHomes databases
- MySQL/MariaDB command line access or database management tool

## Database Schema Differences

### MaSuiteHomes Structure
```sql
masuite_homes:
- id (int) - Primary key
- name (varchar) - Home name
- owner (varchar) - Player UUID
- server (varchar) - Server name
- world (varchar) - World name  
- x, y, z (double) - Coordinates
- yaw, pitch (float) - Rotation
```

### HuskHomes Structure
```sql
huskhomes_users:
- uuid (varchar) - Player UUID
- username (varchar) - Player name
- home_slots (int) - Max homes allowed
- last_position (int) - Reference to position

huskhomes_homes:
- id (int) - Primary key
- uuid (varchar) - Owner UUID
- name (varchar) - Home name
- position_id (int) - Reference to position
- description (text) - Home description
- creation_time (timestamp) - When created
- public (boolean) - Public visibility

huskhomes_position_data:
- id (int) - Primary key
- x, y, z (double) - Coordinates
- yaw, pitch (float) - Rotation
- world_name (varchar) - World name
- world_uuid (varchar) - World UUID
- server_name (varchar) - Server identifier
```

## Migration Steps

### 1. Prepare Server Mapping
Edit the `server_mapping` section in the SQL script to match your server names:

```sql
INSERT INTO server_mapping (masuite_server, huskhomes_server) VALUES
('survival', 'survival'),
('creative', 'creative'),
('skyblock', 'skyblock');
```

### 2. Customize Settings
Adjust these values in the migration script:
- **Home slots**: Default set to 10, modify based on your server's limits
- **ID offset**: Uses +100000 to avoid conflicts with existing HuskHomes data
- **Default descriptions**: Customize the migration description text

### 3. Run Migration
Execute the SQL script on your database:
```bash
mysql -u username -p database_name < MaSuiteHomes_to_HuskHomes_Migration.sql
```

### 4. Verify Results
The script includes verification queries that will show:
- Migration summary with counts
- Any homes missing position data
- Users with duplicate home names
- Sample of migrated data

## Important Considerations

### Home Name Conflicts
- **MaSuiteHomes**: Allows duplicate home names per user
- **HuskHomes**: Requires unique home names per user
- **Solution**: The verification query identifies conflicts that need manual resolution

### Server Names
- **MaSuiteHomes**: Uses descriptive server names
- **HuskHomes**: Uses server IDs from proxy configuration
- **Solution**: Server mapping table converts names appropriately

### World UUIDs
- **MaSuiteHomes**: Doesn't store world UUIDs
- **HuskHomes**: Uses world UUIDs for better identification
- **Solution**: Migration uses placeholder UUID; HuskHomes will update when players visit

### Home Limits
- **MaSuiteHomes**: No built-in home limits
- **HuskHomes**: Enforces home slot limits
- **Solution**: Set appropriate default home slots or use permissions

## Post-Migration Tasks

### 1. Test Home Access
- Have players test their migrated homes: `/home <name>`
- Verify coordinates and world locations are correct
- Check cross-server functionality if applicable

### 2. Update Permissions
Configure HuskHomes permissions for home limits:
```yaml
# Example permission nodes
huskhomes.max_homes.10    # Allow 10 homes
huskhomes.max_homes.20    # Allow 20 homes (VIP)
```

### 3. Configure Home Slots
Adjust default home slots in HuskHomes config:
```yaml
general:
  max_homes: 10  # Default maximum homes
```

### 4. Handle Conflicts
For users with duplicate home names:
1. Identify conflicts using verification query
2. Rename duplicate homes manually
3. Or contact affected players to choose preferred names

## Troubleshooting

### Common Issues

**Migration fails with foreign key errors:**
- Ensure HuskHomes tables exist and are properly structured
- Check that table names match your HuskHomes configuration

**Homes appear but teleportation fails:**
- Verify server names in mapping table match HuskHomes server configuration
- Check world names are correct and worlds exist

**Players can't access migrated homes:**
- Confirm player UUIDs match between systems
- Verify home ownership in `huskhomes_homes` table

**Cross-server homes don't work:**
- Ensure HuskHomes cross-server mode is enabled
- Verify Redis/plugin messaging is configured correctly

### Rollback Procedure
If migration fails or causes issues:

1. **Stop HuskHomes plugin**
2. **Restore database backup**
3. **Fix migration script issues**
4. **Re-run migration**

## Advanced Customization

### Custom Home Descriptions
Modify the description field to include more information:
```sql
CONCAT('Home on ', m.world, ' (', m.server, ') - Migrated from MaSuiteHomes') as description
```

### Selective Migration
Migrate only specific servers or players:
```sql
-- Add WHERE clause to main queries
WHERE m.server IN ('survival', 'creative')
-- OR
WHERE m.owner IN ('uuid1', 'uuid2', 'uuid3')
```

### Preserve Creation Times
If MaSuiteHomes has creation timestamps, use them instead of NOW():
```sql
-- Replace NOW() with actual timestamp column
m.created_at as creation_time
```

## Validation Checklist
- [ ] Database backup completed
- [ ] Server mapping configured correctly
- [ ] Migration script customized for your setup
- [ ] Test migration run on copy database
- [ ] Verification queries show expected results
- [ ] Players can access migrated homes
- [ ] Cross-server functionality works (if applicable)
- [ ] Home limits and permissions configured
- [ ] Duplicate home names resolved
