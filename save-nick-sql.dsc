# MySQL Database Configuration
# Update the database connection details in each task below as needed
# Current settings: localhost:3306, database: savedNicks, table: saved_nicks

saveNickCommand:
    type: command
    name: savenick
    description: Saves a nickname for later/easy use!
    usage: /savenick
    aliases:
    - savednick
    - savednicks
    - nicksave
    - savednickname
    - nicknamesave
    tab complete:
    - if <context.args.size> == 1:
      - determine slot|savecurrent|gui
    - else if <context.args.get[1]> == "slot" && <context.args.size> == 2:
      # Show available slot numbers for /savednick slot <slot>
      - define max_slots 0
      - if <player.has_permission[tlb.nickSave27]>:
        - define max_slots 27
      - else if <player.has_permission[tlb.nickSave18]>:
        - define max_slots 18
      - else if <player.has_permission[tlb.nickSave9]>:
        - define max_slots 9
      - else if <player.has_permission[group.nickname]>:
        - define max_slots 3

      # Build slot list
      - define slot_list <list[]>
      - repeat <[max_slots]> as:slot_num:
        - define slot_list <[slot_list].include[<[slot_num]>]>

      - determine <[slot_list]>
    - else if <context.args.get[1]> == "savecurrent" && <context.args.size> == 2:
      # Show available slot numbers for /savednick savecurrent <slot>
      - define max_slots 0
      - if <player.has_permission[tlb.nickSave27]>:
        - define max_slots 27
      - else if <player.has_permission[tlb.nickSave18]>:
        - define max_slots 18
      - else if <player.has_permission[tlb.nickSave9]>:
        - define max_slots 9
      - else if <player.has_permission[group.nickname]>:
        - define max_slots 3

      # Build slot list
      - define slot_list <list[]>
      - repeat <[max_slots]> as:slot_num:
        - define slot_list <[slot_list].include[<[slot_num]>]>

      - determine <[slot_list]>
    - else:
      - determine <list[]>
    script:
    - if <context.args.get[1]> == "slot" && <player.has_permission[group.nickname]> || <player.has_permission[tlb.nickSave9]> || <player.has_permission[tlb.nickSave18]> || <player.has_permission[tlb.nickSave27]>:
      - if <context.args.get[2].is_decimal>:
        - if <context.args.get[3]||null> != null:
          - if <proc[confirmSlots].context[<context.args.get[2]>|<player>]>:
            - run saveNickToDatabase def:<player>|<context.args.get[2]>|<context.args.get[3]>
            - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&b><&l>Saved nickname <context.args.get[3].parse_color> <&b><&l>to slot <context.args.get[2]>"
            - stop
          - else:
            - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&6>This slot is invalid/you do not have access to it. To save a nick, do /savenick slot <&lt>slot#<&gt> <&lt>nickname<&gt>"
            - stop
          - else:
            - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&6>To save a nick in a slot, do /savenick slot <&lt>slot#<&gt> <&lt>nickname<&gt>"
      - else: 
        - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&6>To save a nick in a slot, do /savenick slot <&lt>slot#<&gt> <&lt>nickname<&gt>"
    - if <context.args.get[1]> == "savecurrent" && <player.has_permission[group.nickname]> || <player.has_permission[tlb.nickSave9]> || <player.has_permission[tlb.nickSave18]> || <player.has_permission[tlb.nickSave27]>:
      - if <context.args.get[2]||null> != null:
        - if <context.args.get[2].is_decimal>:
          - if <proc[confirmSlots].context[<context.args.get[2]>|<player>]>:
            - run saveNickToDatabase def:<player>|<context.args.get[2]>|<player.display_name>
            - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&b><&l>Saved nickname <player.display_name> <&b><&l>to slot <context.args.get[2]>"
            - stop
          - else:
            - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&6>This slot is invalid/you do not have access to it. To save your current nickname, do /savenick savecurrent <&lt>slot<&gt>"
            - stop
        - else:
          - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&6>Slot must be a number! To save your current nickname, do /savenick savecurrent <&lt>slot<&gt>"
          - stop
      - else:
        - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&6>To save your current nickname, do /savenick savecurrent <&lt>slot<&gt>"
        - stop

# /savenick saveother 4 chrismiwggs

    - if <context.args.get[1]||null> == null || <context.args.get[1]||null> == gui:
      - if <player.has_permission[tlb.nickSave27]>:
        - inventory open d:nicksave_27
        - determine FUFILLED
        - stop
      - else if <player.has_permission[tlb.nickSave18]>:
        - inventory open d:nicksave_18
        - determine FUFILLED
        - stop
      - else if <player.has_permission[tlb.nickSave9]>:
        - inventory open d:nicksave_9
        - determine FUFILLED
        - stop
      - else if <player.has_permission[group.nickname]>:
        - inventory open d:nicksave_3
        - determine FUFILLED
        - stop
      - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&6>You do not have this perk! Unlock it on the <&b>/store <&6>or in <&b>/perks<&6>."

    - else if <context.args.get[1]> == saveother && <player.has_permission[tlb.nickSaveAdmin]> || <context.server>:
      - if <context.args.get[2].is_decimal>:
          - run saveNickToDatabase def:<server.match_offline_player[<context.args.get[3]>]>|<context.args.get[2]>|<context.args.get[4]>
          - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&b><&l>Saved nickname <context.args.get[4].parse_color><&b><&l> <&b><&l>for player <context.args.get[3]><&b><&l>, to slot <context.args.get[2]>"
          - stop
      - else:
        - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&6>Slot must be a number! To save a nick, do /savenick slot <&lt>slot#<&gt> <&lt>nickname<&gt>"
        - stop



    - else:
          - narrate "<&6><&l>[<&b><&l>TLB<&6><&l>] <&6>To save a nick, do /savenick savecurrent <&lt>slot<&gt>"
          - stop

confirmSlots:
  type: procedure
  definitions: slot|player
  script:
  - if <[player].has_permission[tlb.nickSave9]>:
    - if <[slot]> > 0 && <[slot]> <= 9:
      - determine true
  
  - else if <[player].has_permission[tlb.nickSave18]>:
    - if <[slot]> > 0 && <[slot]> <= 18:
      - determine true
  
  - else if <[player].has_permission[tlb.nickSave27]>:
    - if <[slot]> > 0 && <[slot]> <= 27:
      - determine true
      
  - else if <[player].has_permission[group.nickname]>:
    - if <[slot]> > 0 && <[slot]> <= 3:
      - determine true
      
  - else:
    - determine false

# MySQL Database Procedures
saveNickToDatabase:
  type: task
  definitions: player|slot|nickname
  script:
  - define host localhost
  - define port 3306
  - define database savedNicks
  - define username superuser
  - define password Kwj0uC#BHtHb#rE9
  - define table saved_nicks
  - sql id:savenick_db connect:<[host]>:<[port]>/<[database]> username:<[username]> password:<[password]>
  - define escaped_nick <[nickname].replace[<&sq>].with[<&sq><&sq>]>
  - define sql_query "INSERT INTO <[table]> (player_uuid, slot_number, nickname) VALUES ('<[player].uuid>', <[slot]>, '<[escaped_nick]>') ON DUPLICATE KEY UPDATE nickname='<[escaped_nick]>'"
  - sql id:savenick_db update:<[sql_query]>
  - sql disconnect id:savenick_db

  # Update the player's flag to match the database
  - flag <[player]> savedNick<[slot]>:<[nickname]>
  - announce to_console "<&7>[Nick Sync] Updated flag for <[player].name> slot <[slot]>: <[nickname]>"

loadNicksFromDatabase:
  type: task
  definitions: player
  script:
  - define host localhost
  - define port 3306
  - define database savedNicks
  - define username superuser
  - define password Kwj0uC#BHtHb#rE9
  - define table saved_nicks
  - sql id:savenick_db connect:<[host]>:<[port]>/<[database]> username:<[username]> password:<[password]>
  - define result <sql[savenick_db].query[SELECT slot_number, nickname FROM <[table]> WHERE player_uuid='<[player].uuid>']>
  - sql disconnect id:savenick_db

  # Clear all existing nickname flags first
  - foreach <list[1|2|3|4|5|6|7|8|9|10|11|12|13|14|15|16|17|18|19|20|21|22|23|24|25|26|27]> as:slot:
    - flag <[player]> savedNick<[slot]>:!

  # Set flags from database data
  - foreach <[result]> as:row:
    - flag <[player]> savedNick<[row].get[1]>:<[row].get[2]>
    - announce to_console "<&7>[Nick Sync] Loaded slot <[row].get[1]> for <[player].name>: <[row].get[2]>"

saveAllNicksToDatabase:
  type: task
  definitions: player
  script:
  - foreach <list[1|2|3|4|5|6|7|8|9|10|11|12|13|14|15|16|17|18|19|20|21|22|23|24|25|26|27]> as:slot:
    - if <[player].flag[savedNick<[slot]>]||null> != null:
      - run saveNickToDatabase def:<[player]>|<[slot]>|<[player].flag[savedNick<[slot]>]>

migratePlayerNicknames:
  type: task
  definitions: player
  script:
  - define migrated_count 0
  - define host localhost
  - define port 3306
  - define database savedNicks
  - define username superuser
  - define password Kwj0uC#BHtHb#rE9
  - define table saved_nicks

  # Determine how many slots this player has access to
  - define max_slots 0
  - if <[player].has_permission[tlb.nickSave27]>:
    - define max_slots 27
  - else if <[player].has_permission[tlb.nickSave18]>:
    - define max_slots 18
  - else if <[player].has_permission[tlb.nickSave9]>:
    - define max_slots 9
  - else if <[player].has_permission[group.nickname]>:
    - define max_slots 3
  - else:
    - announce to_console "<&7>[Nick Migration] <[player].name> has no nickname permissions, skipping migration"
    - stop

  - announce to_console "<&b>[Nick Migration] Starting migration for <[player].name> (max <[max_slots]> slots)"

  # Connect to database
  - sql id:migrate_db connect:<[host]>:<[port]>/<[database]> username:<[username]> password:<[password]>

  # Check each slot up to their permission limit
  - foreach <list[1|2|3|4|5|6|7|8|9|10|11|12|13|14|15|16|17|18|19|20|21|22|23|24|25|26|27]> as:slot:
    - if <[slot]> <= <[max_slots]>:
      - define legacy_nick <[player].flag[savedNick<[slot]>]||null>
      - if <[legacy_nick]> != null:
        - define escaped_nick <[legacy_nick].replace[<&sq>].with[<&sq><&sq>]>
        - define sql_query "INSERT INTO <[table]> (player_uuid, slot_number, nickname) VALUES ('<[player].uuid>', <[slot]>, '<[escaped_nick]>') ON DUPLICATE KEY UPDATE nickname='<[escaped_nick]>'"
        - sql id:migrate_db update:<[sql_query]>
        - define migrated_count <[migrated_count].add[1]>
        - announce to_console "<&7>[Nick Migration] Migrated slot <[slot]> for <[player].name>: <[legacy_nick]>"
        # Note: Don't update flags here since they already exist from the legacy system

  # Disconnect from database
  - sql disconnect id:migrate_db

  - if <[migrated_count]> > 0:
    - announce to_console "<&a>[Nick Migration] Successfully migrated <[migrated_count]> nicknames for <[player].name>"
    # Give the player the migration permission so this doesn't run again
    - execute as_server "lp user <[player].name> permission set tlb.nicknameMigration true"
    - announce to_console "<&a>[Nick Migration] Granted tlb.nicknameMigration permission to <[player].name>"
  - else:
    - announce to_console "<&7>[Nick Migration] No legacy nicknames found for <[player].name>, granting migration permission anyway"
    # Still give them the permission so we don't check again
    - execute as_server "lp user <[player].name> permission set tlb.nicknameMigration true"



returnNick:
  type: procedure
  definitions: slot|player
  script:
  - if <[player].flag[savedNick<[slot]>]||null> == null:
    - determine false
  - else:
    - determine true

getNickFromSlot:
  type: procedure
  definitions: slot|player
  script:
  - determine <[player].flag[savedNick<[slot]>]||"">

nicksave_3:
  type: inventory
  inventory: CHEST
  title: <&5><&l>Saved Nicks - <&9><&l>3 Slots
  size: 9
  gui: true
  slots:
  - [closedSlot] [closedSlot] [closedSlot] [<tern[<proc[returnNick].context[1|<player>]>].pass[nickSlot1].fail[emptySlot]>] [<tern[<proc[returnNick].context[2|<player>]>].pass[nickSlot2].fail[emptySlot]>] [<tern[<proc[returnNick].context[3|<player>]>].pass[nickSlot3].fail[emptySlot]>] [closedSlot] [closedSlot] [closedSlot]

nicksave_9:
  type: inventory
  inventory: CHEST
  title: <&5><&l>Saved Nicks - <&9><&l>9 Slots
  size: 9
  gui: true
  slots:
  - [<tern[<proc[returnNick].context[1|<player>]>].pass[nickSlot1].fail[emptySlot]>] [<tern[<proc[returnNick].context[2|<player>]>].pass[nickSlot2].fail[emptySlot]>] [<tern[<proc[returnNick].context[3|<player>]>].pass[nickSlot3].fail[emptySlot]>] [<tern[<proc[returnNick].context[4|<player>]>].pass[nickSlot4].fail[emptySlot]>] [<tern[<proc[returnNick].context[5|<player>]>].pass[nickSlot5].fail[emptySlot]>] [<tern[<proc[returnNick].context[6|<player>]>].pass[nickSlot6].fail[emptySlot]>] [<tern[<proc[returnNick].context[7|<player>]>].pass[nickSlot7].fail[emptySlot]>] [<tern[<proc[returnNick].context[8|<player>]>].pass[nickSlot8].fail[emptySlot]>] [<tern[<proc[returnNick].context[9|<player>]>].pass[nickSlot9].fail[emptySlot]>]

nicksave_18:
  type: inventory
  inventory: CHEST
  title: <&5><&l>Saved Nicks - <&9><&l>18 Slots
  size: 18
  gui: true
  slots:
  - [<tern[<proc[returnNick].context[1|<player>]>].pass[nickSlot1].fail[emptySlot]>] [<tern[<proc[returnNick].context[2|<player>]>].pass[nickSlot2].fail[emptySlot]>] [<tern[<proc[returnNick].context[3|<player>]>].pass[nickSlot3].fail[emptySlot]>] [<tern[<proc[returnNick].context[4|<player>]>].pass[nickSlot4].fail[emptySlot]>] [<tern[<proc[returnNick].context[5|<player>]>].pass[nickSlot5].fail[emptySlot]>] [<tern[<proc[returnNick].context[6|<player>]>].pass[nickSlot6].fail[emptySlot]>] [<tern[<proc[returnNick].context[7|<player>]>].pass[nickSlot7].fail[emptySlot]>] [<tern[<proc[returnNick].context[8|<player>]>].pass[nickSlot8].fail[emptySlot]>] [<tern[<proc[returnNick].context[9|<player>]>].pass[nickSlot9].fail[emptySlot]>]
  - [<tern[<proc[returnNick].context[10|<player>]>].pass[nickSlot10].fail[emptySlot]>] [<tern[<proc[returnNick].context[11|<player>]>].pass[nickSlot11].fail[emptySlot]>] [<tern[<proc[returnNick].context[12|<player>]>].pass[nickSlot12].fail[emptySlot]>] [<tern[<proc[returnNick].context[13|<player>]>].pass[nickSlot13].fail[emptySlot]>] [<tern[<proc[returnNick].context[14|<player>]>].pass[nickSlot14].fail[emptySlot]>] [<tern[<proc[returnNick].context[15|<player>]>].pass[nickSlot15].fail[emptySlot]>] [<tern[<proc[returnNick].context[16|<player>]>].pass[nickSlot16].fail[emptySlot]>] [<tern[<proc[returnNick].context[17|<player>]>].pass[nickSlot17].fail[emptySlot]>] [<tern[<proc[returnNick].context[18|<player>]>].pass[nickSlot18].fail[emptySlot]>]

nicksave_27:
  type: inventory
  inventory: CHEST
  title: <&5><&l>Saved Nicks - <&9><&l>27 Slots
  size: 27
  gui: true
  slots:
  - [<tern[<proc[returnNick].context[1|<player>]>].pass[nickSlot1].fail[emptySlot]>] [<tern[<proc[returnNick].context[2|<player>]>].pass[nickSlot2].fail[emptySlot]>] [<tern[<proc[returnNick].context[3|<player>]>].pass[nickSlot3].fail[emptySlot]>] [<tern[<proc[returnNick].context[4|<player>]>].pass[nickSlot4].fail[emptySlot]>] [<tern[<proc[returnNick].context[5|<player>]>].pass[nickSlot5].fail[emptySlot]>] [<tern[<proc[returnNick].context[6|<player>]>].pass[nickSlot6].fail[emptySlot]>] [<tern[<proc[returnNick].context[7|<player>]>].pass[nickSlot7].fail[emptySlot]>] [<tern[<proc[returnNick].context[8|<player>]>].pass[nickSlot8].fail[emptySlot]>] [<tern[<proc[returnNick].context[9|<player>]>].pass[nickSlot9].fail[emptySlot]>]
  - [<tern[<proc[returnNick].context[10|<player>]>].pass[nickSlot10].fail[emptySlot]>] [<tern[<proc[returnNick].context[11|<player>]>].pass[nickSlot11].fail[emptySlot]>] [<tern[<proc[returnNick].context[12|<player>]>].pass[nickSlot12].fail[emptySlot]>] [<tern[<proc[returnNick].context[13|<player>]>].pass[nickSlot13].fail[emptySlot]>] [<tern[<proc[returnNick].context[14|<player>]>].pass[nickSlot14].fail[emptySlot]>] [<tern[<proc[returnNick].context[15|<player>]>].pass[nickSlot15].fail[emptySlot]>] [<tern[<proc[returnNick].context[16|<player>]>].pass[nickSlot16].fail[emptySlot]>] [<tern[<proc[returnNick].context[17|<player>]>].pass[nickSlot17].fail[emptySlot]>] [<tern[<proc[returnNick].context[18|<player>]>].pass[nickSlot18].fail[emptySlot]>]
  - [<tern[<proc[returnNick].context[19|<player>]>].pass[nickSlot19].fail[emptySlot]>] [<tern[<proc[returnNick].context[20|<player>]>].pass[nickSlot20].fail[emptySlot]>] [<tern[<proc[returnNick].context[21|<player>]>].pass[nickSlot21].fail[emptySlot]>] [<tern[<proc[returnNick].context[22|<player>]>].pass[nickSlot22].fail[emptySlot]>] [<tern[<proc[returnNick].context[23|<player>]>].pass[nickSlot23].fail[emptySlot]>] [<tern[<proc[returnNick].context[24|<player>]>].pass[nickSlot24].fail[emptySlot]>] [<tern[<proc[returnNick].context[25|<player>]>].pass[nickSlot25].fail[emptySlot]>] [<tern[<proc[returnNick].context[26|<player>]>].pass[nickSlot26].fail[emptySlot]>] [<tern[<proc[returnNick].context[27|<player>]>].pass[nickSlot27].fail[emptySlot]>]
closedSlot:
  type: item
  display name: <&4><&l>LOCKED
  material: BARRIER
  lore:
  - "<&6>Purchase extra saved nicknames on the <&b><&l>/store <&6>or <&b><&l>/perks"
    
    
emptySlot:
  type: item
  display name: <&6><&l>EMPTY SLOT
  material: GLASS_PANE
  lore:
  - "<&b>Use the command <&b><&l>/savenick save <&lt>slot<&gt> <&6>to save a nickname here!"
    
    
nickSlot1:
  type: item
  display name: "<&7><&l>Nick Slot 1"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[1|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot2:
  type: item
  display name: "<&7><&l>Nick Slot 2"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[2|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot3:
  type: item
  display name: "<&7><&l>Nick Slot 3"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[3|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot4:
  type: item
  display name: "<&7><&l>Nick Slot 4"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[4|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot5:
  type: item
  display name: "<&7><&l>Nick Slot 5"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[5|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot6:
  type: item
  display name: "<&7><&l>Nick Slot 6"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[6|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot7:
  type: item
  display name: "<&7><&l>Nick Slot 7"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[7|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot8:
  type: item
  display name: "<&7><&l>Nick Slot 8"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[8|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot9:
  type: item
  display name: "<&7><&l>Nick Slot 9"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[9|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot10:
  type: item
  display name: "<&7><&l>Nick Slot 10"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[10|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot11:
  type: item
  display name: "<&7><&l>Nick Slot 11"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[11|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot12:
  type: item
  display name: "<&7><&l>Nick Slot 12"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[12|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot13:
  type: item
  display name: "<&7><&l>Nick Slot 13"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[13|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot14:
  type: item
  display name: "<&7><&l>Nick Slot 14"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[14|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot15:
  type: item
  display name: "<&7><&l>Nick Slot 15"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[15|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot16:
  type: item
  display name: "<&7><&l>Nick Slot 16"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[16|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot17:
  type: item
  display name: "<&7><&l>Nick Slot 17"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[17|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot18:
  type: item
  display name: "<&7><&l>Nick Slot 18"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[18|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot19:
  type: item
  display name: "<&7><&l>Nick Slot 19"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[19|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot20:
  type: item
  display name: "<&7><&l>Nick Slot 20"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[20|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot21:
  type: item
  display name: "<&7><&l>Nick Slot 21"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[21|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot22:
  type: item
  display name: "<&7><&l>Nick Slot 22"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[22|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot23:
  type: item
  display name: "<&7><&l>Nick Slot 23"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[23|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot24:
  type: item
  display name: "<&7><&l>Nick Slot 24"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[24|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot25:
  type: item
  display name: "<&7><&l>Nick Slot 25"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[25|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot26:
  type: item
  display name: "<&7><&l>Nick Slot 26"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[26|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
nickSlot27:
  type: item
  display name: "<&7><&l>Nick Slot 27"
  material: NETHER_STAR
  lore:
  - "<proc[getNickFromSlot].context[27|<player>].parse_color>"
  - " "
  - "<&6><&l>Click to use this nickname"
    
# Player Join/Leave Events for MySQL Integration and Migration
playerNickEvents:
  type: world
  events:
    on player joins:
    # Check if player needs nickname migration
    - if !<player.has_permission[tlb.nicknameMigration]>:
      - announce to_console "<&b>[Nick Migration] Player <player.name> needs migration, starting process..."
      - run migratePlayerNicknames def:<player>

    # Load nicknames from database and sync flags (only for migrated players)
    - if <player.has_permission[tlb.nicknameMigration]>:
      - announce to_console "<&7>[Nick Sync] Player <player.name> already migrated, syncing flags with database..."
      - run loadNicksFromDatabase def:<player>

    on player quits:
    - run saveAllNicksToDatabase def:<player>

nickSaveListerners:
  type: world
  events:
    on player clicks nickSlot1 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick1].to_minimessage>"
    - inventory close

    on player clicks nickSlot2 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick2].to_minimessage>"
    - inventory close

    on player clicks nickSlot3 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick3].to_minimessage>"
    - inventory close

    on player clicks nickSlot4 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick4].to_minimessage>"
    - inventory close

    on player clicks nickSlot5 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick5].to_minimessage>"
    - inventory close

    on player clicks nickSlot6 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick6].to_minimessage>"
    - inventory close

    on player clicks nickSlot7 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick7].to_minimessage>"
    - inventory close

    on player clicks nickSlot8 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick8].to_minimessage>"
    - inventory close

    on player clicks nickSlot9 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick9].to_minimessage>"
    - inventory close

    on player clicks nickSlot10 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick10].to_minimessage>"
    - inventory close

    on player clicks nickSlot11 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick11].to_minimessage>"
    - inventory close

    on player clicks nickSlot12 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick12].to_minimessage>"
    - inventory close

    on player clicks nickSlot13 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick13].to_minimessage>"
    - inventory close

    on player clicks nickSlot14 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick14].to_minimessage>"
    - inventory close

    on player clicks nickSlot15 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick15].to_minimessage>"
    - inventory close

    on player clicks nickSlot16 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick16].to_minimessage>"
    - inventory close

    on player clicks nickSlot17 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick17].to_minimessage>"
    - inventory close

    on player clicks nickSlot18 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick18].to_minimessage>"
    - inventory close

    on player clicks nickSlot19 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick19].to_minimessage>"
    - inventory close

    on player clicks nickSlot20 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick20].to_minimessage>"
    - inventory close

    on player clicks nickSlot21 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick21].to_minimessage>"
    - inventory close

    on player clicks nickSlot22 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick22].to_minimessage>"
    - inventory close

    on player clicks nickSlot23 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick23].to_minimessage>"
    - inventory close

    on player clicks nickSlot24 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick24].to_minimessage>"
    - inventory close

    on player clicks nickSlot25 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick25].to_minimessage>"
    - inventory close

    on player clicks nickSlot26 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick26].to_minimessage>"
    - inventory close

    on player clicks nickSlot27 in nicksave_3|nicksave_9|nicksave_18|nicksave_27:
    - execute as_server "hexnicks:nickother <player.name> <player.flag[savedNick27].to_minimessage>"
    - inventory close
