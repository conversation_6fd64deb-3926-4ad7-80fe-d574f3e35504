-- =====================================================
-- Advanced MaSuiteHomes to HuskHomes Migration Script
-- =====================================================
-- This advanced script handles edge cases and provides more migration options
-- 
-- Features:
-- - <PERSON><PERSON> duplicate home names
-- - Preserves more metadata
-- - Provides rollback capability
-- - Better error handling
-- =====================================================

-- Configuration variables (modify these as needed)
SET @default_home_slots = 100;
SET @id_offset = 100000;
SET @migration_batch_id = UNIX_TIMESTAMP();

-- Step 1: Create backup tables for rollback capability
CREATE TABLE IF NOT EXISTS migration_backup_homes AS SELECT * FROM huskhomes_homes WHERE 1=0;
CREATE TABLE IF NOT EXISTS migration_backup_positions AS SELECT * FROM huskhomes_position_data WHERE 1=0;
CREATE TABLE IF NOT EXISTS migration_backup_users AS SELECT * FROM huskhomes_users WHERE 1=0;

-- Backup existing data
INSERT INTO migration_backup_homes SELECT * FROM huskhomes_homes;
INSERT INTO migration_backup_positions SELECT * FROM huskhomes_position_data;
INSERT INTO migration_backup_users SELECT * FROM huskhomes_users;

-- Step 2: Create migration tracking table
CREATE TEMPORARY TABLE migration_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    masuite_id INT,
    huskhomes_home_id INT,
    huskhomes_position_id INT,
    player_uuid VARCHAR(36),
    home_name VARCHAR(100),
    status VARCHAR(50),
    notes TEXT,
    migrated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Step 3: Server mapping with validation
CREATE TEMPORARY TABLE server_mapping (
    masuite_server VARCHAR(100),
    huskhomes_server VARCHAR(100),
    is_valid BOOLEAN DEFAULT TRUE
);

-- Insert your server mappings - CUSTOMIZE THIS SECTION
INSERT INTO server_mapping (masuite_server, huskhomes_server) VALUES
('survival', 'survival'),
('creative', 'creative'),
('skyblock', 'skyblock'),
('lobby', 'hub');

-- Mark any unmapped servers as invalid
UPDATE server_mapping sm 
SET is_valid = FALSE 
WHERE sm.huskhomes_server NOT IN (
    SELECT DISTINCT server_name 
    FROM huskhomes_position_data 
    WHERE server_name IS NOT NULL
    LIMIT 1000  -- Safety limit
);

-- Step 4: Handle duplicate home names by appending numbers
CREATE TEMPORARY TABLE processed_homes AS
SELECT 
    m.*,
    CASE 
        WHEN dup.name_count > 1 THEN 
            CONCAT(m.name, '_', 
                ROW_NUMBER() OVER (PARTITION BY m.owner, m.name ORDER BY m.id)
            )
        ELSE m.name
    END as unique_name,
    COALESCE(sm.huskhomes_server, m.server) as target_server,
    sm.is_valid as server_valid
FROM masuite_homes m
LEFT JOIN (
    SELECT owner, name, COUNT(*) as name_count
    FROM masuite_homes 
    GROUP BY owner, name
) dup ON m.owner = dup.owner AND m.name = dup.name
LEFT JOIN server_mapping sm ON m.server = sm.masuite_server;

-- Step 5: Insert users with proper home slot calculation
INSERT IGNORE INTO huskhomes_users (uuid, username, last_position, home_slots, offline)
SELECT 
    ph.owner as uuid,
    'Unknown' as username,
    NULL as last_position,
    GREATEST(@default_home_slots, home_count.total_homes) as home_slots,
    0 as offline
FROM processed_homes ph
JOIN (
    SELECT owner, COUNT(*) as total_homes
    FROM processed_homes
    GROUP BY owner
) home_count ON ph.owner = home_count.owner
GROUP BY ph.owner, home_count.total_homes;

-- Step 6: Insert position data with validation
INSERT INTO huskhomes_position_data (id, x, y, z, yaw, pitch, world_name, world_uuid, server_name)
SELECT 
    ph.id + @id_offset as id,
    COALESCE(ph.x, 0.0) as x,
    COALESCE(ph.y, 64.0) as y,  -- Default to sea level if null
    COALESCE(ph.z, 0.0) as z,
    COALESCE(ph.yaw, 0.0) as yaw,
    COALESCE(ph.pitch, 0.0) as pitch,
    ph.world as world_name,
    '00000000-0000-0000-0000-000000000000' as world_uuid,
    ph.target_server as server_name
FROM processed_homes ph
WHERE ph.server_valid = TRUE;

-- Log position insertions
INSERT INTO migration_log (masuite_id, huskhomes_position_id, player_uuid, home_name, status, notes)
SELECT 
    ph.id,
    ph.id + @id_offset,
    ph.owner,
    ph.unique_name,
    CASE WHEN ph.server_valid THEN 'POSITION_CREATED' ELSE 'POSITION_SKIPPED' END,
    CASE WHEN ph.server_valid THEN 'Position data migrated successfully' 
         ELSE CONCAT('Skipped - invalid server: ', ph.server) END
FROM processed_homes ph;

-- Step 7: Insert home data
INSERT INTO huskhomes_homes (id, uuid, name, position_id, description, creation_time, public)
SELECT 
    ph.id,
    ph.owner as uuid,
    ph.unique_name as name,
    ph.id + @id_offset as position_id,
    CONCAT(
        'Migrated from MaSuiteHomes',
        CASE WHEN ph.name != ph.unique_name 
             THEN CONCAT(' (original name: ', ph.name, ')') 
             ELSE '' END,
        ' | Server: ', ph.server,
        ' | World: ', ph.world
    ) as description,
    NOW() as creation_time,
    0 as public
FROM processed_homes ph
WHERE ph.server_valid = TRUE
AND EXISTS (
    SELECT 1 FROM huskhomes_position_data 
    WHERE id = ph.id + @id_offset
);

-- Log home insertions
INSERT INTO migration_log (masuite_id, huskhomes_home_id, huskhomes_position_id, player_uuid, home_name, status, notes)
SELECT 
    ph.id,
    ph.id,
    ph.id + @id_offset,
    ph.owner,
    ph.unique_name,
    'HOME_CREATED',
    CASE WHEN ph.name != ph.unique_name 
         THEN CONCAT('Home renamed from "', ph.name, '" to "', ph.unique_name, '" due to duplicate')
         ELSE 'Home migrated successfully' END
FROM processed_homes ph
WHERE ph.server_valid = TRUE
AND EXISTS (
    SELECT 1 FROM huskhomes_homes 
    WHERE id = ph.id
);

-- Step 8: Generate comprehensive migration report
SELECT 
    'MIGRATION SUMMARY' as report_section,
    '' as detail,
    '' as count;

SELECT 
    'Total MaSuite Homes' as report_section,
    '' as detail,
    COUNT(*) as count
FROM masuite_homes;

SELECT 
    'Successfully Migrated' as report_section,
    '' as detail,
    COUNT(*) as count
FROM migration_log 
WHERE status = 'HOME_CREATED';

SELECT 
    'Homes Renamed (Duplicates)' as report_section,
    '' as detail,
    COUNT(*) as count
FROM migration_log 
WHERE notes LIKE '%renamed%';

SELECT 
    'Skipped (Invalid Server)' as report_section,
    '' as detail,
    COUNT(*) as count
FROM migration_log 
WHERE status = 'POSITION_SKIPPED';

SELECT 
    'Players Migrated' as report_section,
    '' as detail,
    COUNT(DISTINCT player_uuid) as count
FROM migration_log 
WHERE status = 'HOME_CREATED';

-- Detailed breakdown by server
SELECT 
    'MIGRATION BY SERVER' as report_section,
    ph.server as detail,
    COUNT(*) as count
FROM processed_homes ph
JOIN migration_log ml ON ph.id = ml.masuite_id
WHERE ml.status = 'HOME_CREATED'
GROUP BY ph.server
ORDER BY count DESC;

-- Show any issues that need attention
SELECT 
    'ISSUES REQUIRING ATTENTION' as report_section,
    status as detail,
    COUNT(*) as count
FROM migration_log 
WHERE status != 'HOME_CREATED'
GROUP BY status;

-- Show renamed homes for admin review
SELECT 
    'RENAMED HOMES' as report_section,
    CONCAT(player_uuid, ': ', notes) as detail,
    '' as count
FROM migration_log 
WHERE notes LIKE '%renamed%'
LIMIT 20;

-- Cleanup temporary tables
DROP TEMPORARY TABLE server_mapping;
DROP TEMPORARY TABLE processed_homes;
DROP TEMPORARY TABLE migration_log;
